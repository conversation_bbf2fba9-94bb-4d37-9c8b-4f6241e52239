#!/usr/bin/env python3
"""
Demo script to show the difference in chart sizing
"""

import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import pandas as pd
import numpy as np

def create_sample_data():
    """Create sample options data for demonstration"""
    strikes = np.arange(180, 220, 2.5)
    np.random.seed(42)  # For reproducible results
    
    # Simulate some realistic options data
    call_premiums = np.random.exponential(2, len(strikes)) * 1e6
    put_premiums = np.random.exponential(1.5, len(strikes)) * 1e6
    
    # Make it more realistic - calls higher at lower strikes, puts higher at higher strikes
    call_premiums = call_premiums * np.exp(-(strikes - 200)**2 / 200)
    put_premiums = put_premiums * np.exp(-(strikes - 210)**2 / 300)
    
    net_premium = call_premiums - put_premiums
    colors = ['green' if x > 0 else 'red' for x in net_premium]
    
    return strikes, net_premium, colors

def create_old_style_chart():
    """Create chart with old sizing"""
    strikes, net_premium, colors = create_sample_data()
    
    fig, ax = plt.subplots(figsize=(8, 6))  # Old small size
    bars = ax.barh(strikes.astype(str), net_premium/1e6, color=colors)
    ax.axvline(0, color='black', linewidth=0.8)
    
    ax.set_xlabel('Net Premium (Millions USD)')
    ax.set_ylabel('Strike')
    ax.set_title('DEMO - Old Style (Small Chart)')
    ax.grid(axis='x', linestyle='--', alpha=0.5)
    ax.invert_yaxis()
    
    plt.tight_layout()
    plt.savefig('old_style_chart.png', dpi=100, bbox_inches='tight')
    plt.close()
    print("✓ Created old_style_chart.png (small)")

def create_new_style_chart():
    """Create chart with new improved sizing"""
    strikes, net_premium, colors = create_sample_data()
    
    # New large size
    fig_width = 20
    fig_height = max(16, len(strikes) * 0.6)
    fig, ax = plt.subplots(figsize=(fig_width, fig_height))
    
    bars = ax.barh(strikes.astype(str), net_premium/1e6, color=colors, height=0.9)
    ax.axvline(0, color='black', linewidth=2)
    
    # Much better formatting with larger fonts
    ax.set_xlabel('Net Premium (Millions USD)', fontsize=18, fontweight='bold')
    ax.set_ylabel('Strike Price', fontsize=18, fontweight='bold')
    ax.set_title('DEMO - New Style (Large Chart)', fontsize=22, fontweight='bold', pad=30)
    ax.grid(axis='x', linestyle='--', alpha=0.7, linewidth=1)
    ax.invert_yaxis()
    
    # Much larger tick labels
    ax.tick_params(axis='both', which='major', labelsize=14)
    
    # Add value labels on bars
    for i, (bar, value) in enumerate(zip(bars, net_premium/1e6)):
        if abs(value) > 0.1:  # Only label significant values
            ax.text(value + (0.1 if value > 0 else -0.1), bar.get_y() + bar.get_height()/2, 
                   f'{value:.1f}M', ha='left' if value > 0 else 'right', va='center', fontsize=10)
    
    plt.subplots_adjust(left=0.15, right=0.95, top=0.92, bottom=0.12)
    plt.savefig('new_style_chart.png', dpi=200, bbox_inches='tight', 
               facecolor='white', edgecolor='none', pad_inches=0.5)
    plt.close()
    print("✓ Created new_style_chart.png (large, high quality)")

if __name__ == "__main__":
    print("Creating demo charts to show size difference...")
    print("=" * 50)
    
    create_old_style_chart()
    create_new_style_chart()
    
    print("\n" + "=" * 50)
    print("Demo charts created!")
    print("\nFile sizes:")
    
    import os
    if os.path.exists('old_style_chart.png'):
        size = os.path.getsize('old_style_chart.png')
        print(f"old_style_chart.png: {size:,} bytes")
    
    if os.path.exists('new_style_chart.png'):
        size = os.path.getsize('new_style_chart.png')
        print(f"new_style_chart.png: {size:,} bytes")
    
    print("\nThe new style chart should be:")
    print("- Much larger and easier to read")
    print("- Higher resolution (200 DPI vs 100 DPI)")
    print("- Better fonts and formatting")
    print("- Value labels on significant bars")
    print("- More professional appearance")
