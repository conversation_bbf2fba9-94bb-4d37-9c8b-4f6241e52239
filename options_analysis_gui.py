import sys
import yfinance as yf
import pandas as pd
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
import matplotlib.pyplot as plt
from matplotlib.figure import Figure
import io
import base64

from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                             QHBoxLayout, QLineEdit, QPushButton, QComboBox,
                             QLabel, QTextEdit, QSplitter, QMessageBox, QProgressBar,
                             QScrollArea)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QByteArray
from PyQt6.QtGui import QFont, QPixmap


class OptionsDataWorker(QThread):
    """Worker thread for fetching options data to prevent GUI freezing"""
    data_ready = pyqtSignal(object, str, str)  # chain, ticker, exp_date
    error_occurred = pyqtSignal(str)
    progress_update = pyqtSignal(str)
    
    def __init__(self, ticker, exp_date=None):
        super().__init__()
        self.ticker = ticker
        self.exp_date = exp_date
        
    def run(self):
        try:
            self.progress_update.emit(f"Validating ticker {self.ticker}...")
            stock = yf.Ticker(self.ticker)
            
            # Validate ticker
            info = stock.info
            if 'symbol' not in info or not info.get('symbol'):
                self.error_occurred.emit(f"Ticker '{self.ticker}' not found.")
                return
                
            # Check if options are available
            if not hasattr(stock, 'options') or not stock.options:
                self.error_occurred.emit(f"No options data available for '{self.ticker}'.")
                return
                
            # Use provided expiration date or get the nearest one
            if self.exp_date is None:
                self.exp_date = stock.options[0]
                
            self.progress_update.emit(f"Fetching options data for {self.exp_date}...")
            chain = stock.option_chain(self.exp_date)
            
            self.data_ready.emit(chain, self.ticker, self.exp_date)
            
        except Exception as e:
            self.error_occurred.emit(f"Error fetching data: {str(e)}")


class MatplotlibWidget(QWidget):
    """Custom widget to display matplotlib plots as images"""
    def __init__(self):
        super().__init__()
        self.image_label = QLabel()
        self.image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.image_label.setText("No plot to display")
        self.image_label.setStyleSheet("border: 1px solid gray; background-color: white;")

        # Scroll area for large plots
        scroll_area = QScrollArea()
        scroll_area.setWidget(self.image_label)
        scroll_area.setWidgetResizable(True)

        layout = QVBoxLayout()
        layout.addWidget(scroll_area)
        self.setLayout(layout)

    def clear_plot(self):
        self.image_label.setText("No plot to display")
        self.image_label.setPixmap(QPixmap())

    def plot_options_data(self, chain, ticker, exp_date):
        """Create the options analysis plot and display as image"""
        # Process the data (same logic as original script)
        calls = chain.calls.copy()
        puts = chain.puts.copy()
        calls['premium_spent'] = calls['volume'] * calls['lastPrice'] * 100
        puts['premium_spent'] = puts['volume'] * puts['lastPrice'] * 100

        # Merge calls & puts on strike
        df = pd.merge(calls[['strike','premium_spent']],
                      puts[['strike','premium_spent']],
                      on='strike', how='outer',
                      suffixes=('_call','_put')).fillna(0)

        # Compute net premium
        df['net_premium'] = df['premium_spent_call'] - df['premium_spent_put']
        df = df.sort_values('strike')
        colors = df['net_premium'].apply(lambda x: 'green' if x>0 else 'red')

        # Create the plot
        fig, ax = plt.subplots(figsize=(12, 8))
        bars = ax.barh(df['strike'].astype(str), df['net_premium']/1e6, color=colors)
        ax.axvline(0, color='black', linewidth=0.8)

        ax.set_xlabel('Net Premium (Millions USD)')
        ax.set_ylabel('Strike Price')
        ax.set_title(f'{ticker} Net Premium by Strike ({exp_date})')
        ax.grid(axis='x', linestyle='--', alpha=0.5)
        ax.invert_yaxis()

        plt.tight_layout()

        # Save plot to bytes buffer
        buffer = io.BytesIO()
        plt.savefig(buffer, format='png', dpi=100, bbox_inches='tight')
        buffer.seek(0)

        # Convert to QPixmap and display
        pixmap = QPixmap()
        pixmap.loadFromData(buffer.getvalue())
        self.image_label.setPixmap(pixmap)
        self.image_label.setText("")

        plt.close(fig)  # Clean up
        buffer.close()


class OptionsAnalysisGUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.current_stock = None
        self.current_options_dates = []
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle("Options Analysis Tool")
        self.setGeometry(100, 100, 1200, 800)
        
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QVBoxLayout(central_widget)
        
        # Input section
        input_layout = QHBoxLayout()
        
        # Ticker input
        input_layout.addWidget(QLabel("Ticker:"))
        self.ticker_input = QLineEdit()
        self.ticker_input.setPlaceholderText("Enter ticker symbol (e.g., AAPL, MSFT)")
        self.ticker_input.returnPressed.connect(self.fetch_ticker_data)
        input_layout.addWidget(self.ticker_input)
        
        # Fetch button
        self.fetch_button = QPushButton("Fetch Options")
        self.fetch_button.clicked.connect(self.fetch_ticker_data)
        input_layout.addWidget(self.fetch_button)
        
        # Expiration date selector
        input_layout.addWidget(QLabel("Expiration:"))
        self.exp_combo = QComboBox()
        self.exp_combo.currentTextChanged.connect(self.on_expiration_changed)
        input_layout.addWidget(self.exp_combo)
        
        # Analyze button
        self.analyze_button = QPushButton("Analyze")
        self.analyze_button.clicked.connect(self.analyze_options)
        self.analyze_button.setEnabled(False)
        input_layout.addWidget(self.analyze_button)
        
        main_layout.addLayout(input_layout)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        main_layout.addWidget(self.progress_bar)
        
        # Status label
        self.status_label = QLabel("Enter a ticker symbol to begin")
        main_layout.addWidget(self.status_label)
        
        # Splitter for plot and info
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # Plot widget
        self.plot_widget = MatplotlibWidget()
        splitter.addWidget(self.plot_widget)
        
        # Info panel
        self.info_panel = QTextEdit()
        self.info_panel.setMaximumWidth(300)
        self.info_panel.setReadOnly(True)
        self.info_panel.setPlainText("Options analysis information will appear here...")
        splitter.addWidget(self.info_panel)
        
        splitter.setSizes([800, 300])
        main_layout.addWidget(splitter)
        
    def fetch_ticker_data(self):
        """Fetch ticker data and populate expiration dates"""
        ticker = self.ticker_input.text().strip().upper()
        if not ticker:
            QMessageBox.warning(self, "Warning", "Please enter a ticker symbol")
            return
            
        self.fetch_button.setEnabled(False)
        self.analyze_button.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # Indeterminate progress
        self.status_label.setText(f"Fetching data for {ticker}...")
        
        # Clear previous data
        self.exp_combo.clear()
        self.plot_widget.clear_plot()
        
        # Start worker thread
        self.worker = OptionsDataWorker(ticker)
        self.worker.data_ready.connect(self.on_ticker_data_ready)
        self.worker.error_occurred.connect(self.on_error)
        self.worker.progress_update.connect(self.on_progress_update)
        self.worker.finished.connect(self.on_worker_finished)
        self.worker.start()
        
    def on_ticker_data_ready(self, chain, ticker, exp_date):
        """Handle successful ticker data fetch"""
        self.current_stock = yf.Ticker(ticker)
        self.current_options_dates = self.current_stock.options
        
        # Populate expiration dates combo box
        self.exp_combo.addItems(self.current_options_dates[:10])  # Show first 10 dates
        
        self.status_label.setText(f"Data loaded for {ticker}. Select expiration date and click Analyze.")
        self.analyze_button.setEnabled(True)
        
        # Update info panel
        info_text = f"Ticker: {ticker}\n"
        info_text += f"Available expiration dates: {len(self.current_options_dates)}\n"
        info_text += f"Current selection: {self.exp_combo.currentText()}\n\n"
        info_text += "Select an expiration date and click 'Analyze' to view the options analysis."
        self.info_panel.setPlainText(info_text)
        
    def on_expiration_changed(self):
        """Handle expiration date selection change"""
        if self.current_stock and self.exp_combo.currentText():
            self.analyze_button.setEnabled(True)
            
    def analyze_options(self):
        """Analyze options for selected expiration date"""
        if not self.current_stock or not self.exp_combo.currentText():
            return
            
        exp_date = self.exp_combo.currentText()
        ticker = self.ticker_input.text().strip().upper()
        
        self.analyze_button.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)
        self.status_label.setText(f"Analyzing options for {exp_date}...")
        
        # Start analysis worker
        self.analysis_worker = OptionsDataWorker(ticker, exp_date)
        self.analysis_worker.data_ready.connect(self.on_analysis_ready)
        self.analysis_worker.error_occurred.connect(self.on_error)
        self.analysis_worker.progress_update.connect(self.on_progress_update)
        self.analysis_worker.finished.connect(self.on_worker_finished)
        self.analysis_worker.start()
        
    def on_analysis_ready(self, chain, ticker, exp_date):
        """Handle successful options analysis"""
        # Create the plot
        self.plot_widget.plot_options_data(chain, ticker, exp_date)
        
        # Update info panel with analysis details
        calls = chain.calls
        puts = chain.puts
        
        info_text = f"Analysis Results for {ticker}\n"
        info_text += f"Expiration Date: {exp_date}\n\n"
        info_text += f"Call Options: {len(calls)} contracts\n"
        info_text += f"Put Options: {len(puts)} contracts\n\n"
        
        if len(calls) > 0:
            total_call_volume = calls['volume'].sum()
            total_call_premium = (calls['volume'] * calls['lastPrice'] * 100).sum()
            info_text += f"Total Call Volume: {total_call_volume:,.0f}\n"
            info_text += f"Total Call Premium: ${total_call_premium:,.0f}\n\n"
            
        if len(puts) > 0:
            total_put_volume = puts['volume'].sum()
            total_put_premium = (puts['volume'] * puts['lastPrice'] * 100).sum()
            info_text += f"Total Put Volume: {total_put_volume:,.0f}\n"
            info_text += f"Total Put Premium: ${total_put_premium:,.0f}\n\n"
            
        info_text += "Green bars: Net call premium (bullish sentiment)\n"
        info_text += "Red bars: Net put premium (bearish sentiment)"
        
        self.info_panel.setPlainText(info_text)
        self.status_label.setText(f"Analysis complete for {ticker} ({exp_date})")
        
    def on_error(self, error_message):
        """Handle errors"""
        QMessageBox.critical(self, "Error", error_message)
        self.status_label.setText("Error occurred. Please try again.")
        
    def on_progress_update(self, message):
        """Handle progress updates"""
        self.status_label.setText(message)
        
    def on_worker_finished(self):
        """Handle worker thread completion"""
        self.progress_bar.setVisible(False)
        self.fetch_button.setEnabled(True)
        if self.current_stock:
            self.analyze_button.setEnabled(True)


def main():
    app = QApplication(sys.argv)
    window = OptionsAnalysisGUI()
    window.show()
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
