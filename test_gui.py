#!/usr/bin/env python3
"""
Simple test script to verify the GUI components work correctly
"""

import sys
import yfinance as yf
from options_analysis_gui import OptionsAnalysisGUI
from PyQt6.QtWidgets import QApplication

def test_ticker_validation():
    """Test ticker validation functionality"""
    print("Testing ticker validation...")
    
    # Test with a known ticker
    try:
        stock = yf.Ticker("AAPL")
        info = stock.info
        if 'symbol' in info and info.get('symbol'):
            print("✓ AAPL ticker validation passed")
        else:
            print("✗ AAPL ticker validation failed")
    except Exception as e:
        print(f"✗ Error testing AAPL: {e}")
    
    # Test options availability
    try:
        stock = yf.Ticker("AAPL")
        if hasattr(stock, 'options') and stock.options:
            print("✓ AAPL options data available")
            print(f"  Available expiration dates: {len(stock.options)}")
            print(f"  First few dates: {stock.options[:3]}")
        else:
            print("✗ AAPL options data not available")
    except Exception as e:
        print(f"✗ Error checking AAPL options: {e}")

def test_gui_components():
    """Test GUI components without actually showing the window"""
    print("\nTesting GUI components...")
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Create the main window (but don't show it)
        window = OptionsAnalysisGUI()
        
        # Test basic component creation
        if hasattr(window, 'ticker_input'):
            print("✓ Ticker input field created")
        else:
            print("✗ Ticker input field missing")
            
        if hasattr(window, 'exp_combo'):
            print("✓ Expiration combo box created")
        else:
            print("✗ Expiration combo box missing")
            
        if hasattr(window, 'plot_widget'):
            print("✓ Plot widget created")
        else:
            print("✗ Plot widget missing")
            
        if hasattr(window, 'info_panel'):
            print("✓ Info panel created")
        else:
            print("✗ Info panel missing")
            
        print("✓ GUI components test completed successfully")
        
    except Exception as e:
        print(f"✗ Error testing GUI components: {e}")

if __name__ == "__main__":
    print("Options Analysis GUI Test Suite")
    print("=" * 40)
    
    test_ticker_validation()
    test_gui_components()
    
    print("\n" + "=" * 40)
    print("Test suite completed!")
    print("\nTo run the full GUI application, execute:")
    print("python options_analysis_gui.py")
