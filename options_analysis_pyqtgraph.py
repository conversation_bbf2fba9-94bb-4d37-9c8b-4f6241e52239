import sys
import yfinance as yf
import pandas as pd
import numpy as np
import pyqtgraph as pg
from pyqtgraph.Qt import QtCore, QtWidgets
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QLineEdit, QPushButton, QComboBox, 
                             QLabel, QTextEdit, QSplitter, QMessageBox, QProgressBar)
from PyQt6.QtCore import Qt, QThread, pyqtSignal
from PyQt6.QtGui import QFont

# Configure PyQtGraph
pg.setConfigOptions(antialias=True)
pg.setConfigOption('background', 'w')  # White background
pg.setConfigOption('foreground', 'k')  # Black foreground


class OptionsDataWorker(QThread):
    """Worker thread for fetching options data to prevent GUI freezing"""
    data_ready = pyqtSignal(object, str, str)  # chain, ticker, exp_date
    error_occurred = pyqtSignal(str)
    progress_update = pyqtSignal(str)
    
    def __init__(self, ticker, exp_date=None):
        super().__init__()
        self.ticker = ticker
        self.exp_date = exp_date
        
    def run(self):
        try:
            self.progress_update.emit(f"Validating ticker {self.ticker}...")
            stock = yf.Ticker(self.ticker)
            
            # Validate ticker
            info = stock.info
            if 'symbol' not in info or not info.get('symbol'):
                self.error_occurred.emit(f"Ticker '{self.ticker}' not found.")
                return
                
            # Check if options are available
            if not hasattr(stock, 'options') or not stock.options:
                self.error_occurred.emit(f"No options data available for '{self.ticker}'.")
                return
                
            # Use provided expiration date or get the nearest one
            if self.exp_date is None:
                self.exp_date = stock.options[0]
                
            self.progress_update.emit(f"Fetching options data for {self.exp_date}...")
            chain = stock.option_chain(self.exp_date)
            
            self.data_ready.emit(chain, self.ticker, self.exp_date)
            
        except Exception as e:
            self.error_occurred.emit(f"Error fetching data: {str(e)}")


class PyQtGraphWidget(QWidget):
    """Custom widget using PyQtGraph for high-performance plotting"""
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        layout = QVBoxLayout()
        
        # Create the plot widget
        self.plot_widget = pg.PlotWidget()
        self.plot_widget.setLabel('left', 'Strike Price', **{'font-size': '14pt', 'font-weight': 'bold'})
        self.plot_widget.setLabel('bottom', 'Net Premium (Millions USD)', **{'font-size': '14pt', 'font-weight': 'bold'})
        self.plot_widget.showGrid(x=True, y=True, alpha=0.3)
        
        # Set minimum size for better visibility
        self.plot_widget.setMinimumSize(800, 600)
        
        layout.addWidget(self.plot_widget)
        self.setLayout(layout)
        
    def clear_plot(self):
        """Clear the plot"""
        self.plot_widget.clear()
        
    def plot_options_data(self, chain, ticker, exp_date):
        """Create the options analysis plot using PyQtGraph"""
        self.clear_plot()
        
        # Process the data (same logic as original script)
        calls = chain.calls.copy()
        puts = chain.puts.copy()
        calls['premium_spent'] = calls['volume'] * calls['lastPrice'] * 100
        puts['premium_spent'] = puts['volume'] * puts['lastPrice'] * 100
        
        # Merge calls & puts on strike
        df = pd.merge(calls[['strike','premium_spent']],
                      puts[['strike','premium_spent']],
                      on='strike', how='outer',
                      suffixes=('_call','_put')).fillna(0)
        
        # Compute net premium
        df['net_premium'] = df['premium_spent_call'] - df['premium_spent_put']
        df = df.sort_values('strike')
        
        # Prepare data for horizontal bar chart
        y_positions = np.arange(len(df))
        net_premiums = df['net_premium'].values / 1e6  # Convert to millions
        strikes = df['strike'].values
        
        # Create colors: green for positive (calls), red for negative (puts)
        colors = []
        for premium in net_premiums:
            if premium > 0:
                colors.append((0, 150, 0, 180))  # Green with transparency
            else:
                colors.append((200, 0, 0, 180))  # Red with transparency
        
        # Create horizontal bar chart using BarGraphItem
        bar_height = 0.8
        bar_items = []
        
        for i, (y_pos, premium, color) in enumerate(zip(y_positions, net_premiums, colors)):
            if premium != 0:  # Only plot non-zero bars
                # Create individual bar
                bar = pg.BarGraphItem(
                    x=[0], y=[y_pos], width=[abs(premium)], height=bar_height,
                    brush=color, pen=pg.mkPen(color='black', width=1)
                )
                
                # Adjust position for negative values
                if premium < 0:
                    bar.setOpts(x=[premium])
                
                self.plot_widget.addItem(bar)
        
        # Add zero line
        zero_line = pg.InfiniteLine(pos=0, angle=90, pen=pg.mkPen('black', width=2))
        self.plot_widget.addItem(zero_line)
        
        # Set up the plot
        self.plot_widget.setTitle(f'{ticker} Net Premium by Strike ({exp_date})', 
                                 **{'font-size': '16pt', 'font-weight': 'bold'})
        
        # Set Y-axis to show strike prices (inverted so highest strikes are at top)
        y_ticks = [(i, f"{strike:.1f}") for i, strike in enumerate(strikes)]
        y_axis = self.plot_widget.getAxis('left')
        y_axis.setTicks([y_ticks])
        
        # Set axis ranges
        self.plot_widget.setYRange(-0.5, len(df) - 0.5)
        
        # Auto-range X axis with some padding
        if len(net_premiums) > 0:
            x_min = min(net_premiums.min(), 0) * 1.1
            x_max = max(net_premiums.max(), 0) * 1.1
            if x_min == x_max == 0:
                x_min, x_max = -1, 1
            self.plot_widget.setXRange(x_min, x_max)
        
        # Invert Y-axis so highest strikes appear at top
        self.plot_widget.invertY(True)
        
        # Add text annotations for significant values
        for i, (y_pos, premium, strike) in enumerate(zip(y_positions, net_premiums, strikes)):
            if abs(premium) > 0.1:  # Only annotate significant values
                text_x = premium + (0.1 if premium > 0 else -0.1)
                text_item = pg.TextItem(f'{premium:.1f}M', anchor=(0, 0.5))
                text_item.setPos(text_x, y_pos)
                text_item.setFont(QFont('Arial', 10))
                self.plot_widget.addItem(text_item)


class OptionsAnalysisGUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.current_stock = None
        self.current_options_dates = []
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle("Options Analysis Tool - PyQtGraph Version")
        self.setGeometry(100, 100, 1600, 1000)
        self.setMinimumSize(1400, 900)
        
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QVBoxLayout(central_widget)
        
        # Input section
        input_layout = QHBoxLayout()
        
        # Ticker input
        input_layout.addWidget(QLabel("Ticker:"))
        self.ticker_input = QLineEdit()
        self.ticker_input.setPlaceholderText("Enter ticker symbol (e.g., AAPL, MSFT)")
        self.ticker_input.returnPressed.connect(self.fetch_ticker_data)
        input_layout.addWidget(self.ticker_input)
        
        # Fetch button
        self.fetch_button = QPushButton("Fetch Options")
        self.fetch_button.clicked.connect(self.fetch_ticker_data)
        input_layout.addWidget(self.fetch_button)
        
        # Expiration date selector
        input_layout.addWidget(QLabel("Expiration:"))
        self.exp_combo = QComboBox()
        self.exp_combo.currentTextChanged.connect(self.on_expiration_changed)
        input_layout.addWidget(self.exp_combo)
        
        # Analyze button
        self.analyze_button = QPushButton("Analyze")
        self.analyze_button.clicked.connect(self.analyze_options)
        self.analyze_button.setEnabled(False)
        input_layout.addWidget(self.analyze_button)
        
        main_layout.addLayout(input_layout)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        main_layout.addWidget(self.progress_bar)
        
        # Status label
        self.status_label = QLabel("Enter a ticker symbol to begin")
        main_layout.addWidget(self.status_label)
        
        # Splitter for plot and info
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # Plot widget - using PyQtGraph
        self.plot_widget = PyQtGraphWidget()
        splitter.addWidget(self.plot_widget)
        
        # Info panel
        self.info_panel = QTextEdit()
        self.info_panel.setMaximumWidth(350)
        self.info_panel.setMinimumWidth(300)
        self.info_panel.setReadOnly(True)
        self.info_panel.setPlainText("Options analysis information will appear here...")
        splitter.addWidget(self.info_panel)
        
        # Give much more space to the chart
        splitter.setSizes([1200, 350])
        main_layout.addWidget(splitter)
        
    def fetch_ticker_data(self):
        """Fetch ticker data and populate expiration dates"""
        ticker = self.ticker_input.text().strip().upper()
        if not ticker:
            QMessageBox.warning(self, "Warning", "Please enter a ticker symbol")
            return
            
        self.fetch_button.setEnabled(False)
        self.analyze_button.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # Indeterminate progress
        self.status_label.setText(f"Fetching data for {ticker}...")
        
        # Clear previous data
        self.exp_combo.clear()
        self.plot_widget.clear_plot()
        
        # Start worker thread
        self.worker = OptionsDataWorker(ticker)
        self.worker.data_ready.connect(self.on_ticker_data_ready)
        self.worker.error_occurred.connect(self.on_error)
        self.worker.progress_update.connect(self.on_progress_update)
        self.worker.finished.connect(self.on_worker_finished)
        self.worker.start()
        
    def on_ticker_data_ready(self, chain, ticker, exp_date):
        """Handle successful ticker data fetch"""
        self.current_stock = yf.Ticker(ticker)
        self.current_options_dates = self.current_stock.options
        
        # Populate expiration dates combo box
        self.exp_combo.addItems(self.current_options_dates[:10])  # Show first 10 dates
        
        self.status_label.setText(f"Data loaded for {ticker}. Select expiration date and click Analyze.")
        self.analyze_button.setEnabled(True)
        
        # Update info panel
        info_text = f"Ticker: {ticker}\n"
        info_text += f"Available expiration dates: {len(self.current_options_dates)}\n"
        info_text += f"Current selection: {self.exp_combo.currentText()}\n\n"
        info_text += "Select an expiration date and click 'Analyze' to view the options analysis."
        self.info_panel.setPlainText(info_text)
        
    def on_expiration_changed(self):
        """Handle expiration date selection change"""
        if self.current_stock and self.exp_combo.currentText():
            self.analyze_button.setEnabled(True)
            
    def analyze_options(self):
        """Analyze options for selected expiration date"""
        if not self.current_stock or not self.exp_combo.currentText():
            return
            
        exp_date = self.exp_combo.currentText()
        ticker = self.ticker_input.text().strip().upper()
        
        self.analyze_button.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)
        self.status_label.setText(f"Analyzing options for {exp_date}...")
        
        # Start analysis worker
        self.analysis_worker = OptionsDataWorker(ticker, exp_date)
        self.analysis_worker.data_ready.connect(self.on_analysis_ready)
        self.analysis_worker.error_occurred.connect(self.on_error)
        self.analysis_worker.progress_update.connect(self.on_progress_update)
        self.analysis_worker.finished.connect(self.on_worker_finished)
        self.analysis_worker.start()
        
    def on_analysis_ready(self, chain, ticker, exp_date):
        """Handle successful options analysis"""
        # Create the plot
        self.plot_widget.plot_options_data(chain, ticker, exp_date)
        
        # Update info panel with analysis details
        calls = chain.calls
        puts = chain.puts
        
        info_text = f"Analysis Results for {ticker}\n"
        info_text += f"Expiration Date: {exp_date}\n\n"
        info_text += f"Call Options: {len(calls)} contracts\n"
        info_text += f"Put Options: {len(puts)} contracts\n\n"
        
        if len(calls) > 0:
            total_call_volume = calls['volume'].sum()
            total_call_premium = (calls['volume'] * calls['lastPrice'] * 100).sum()
            info_text += f"Total Call Volume: {total_call_volume:,.0f}\n"
            info_text += f"Total Call Premium: ${total_call_premium:,.0f}\n\n"
            
        if len(puts) > 0:
            total_put_volume = puts['volume'].sum()
            total_put_premium = (puts['volume'] * puts['lastPrice'] * 100).sum()
            info_text += f"Total Put Volume: {total_put_volume:,.0f}\n"
            info_text += f"Total Put Premium: ${total_put_premium:,.0f}\n\n"
            
        info_text += "Green bars: Net call premium (bullish sentiment)\n"
        info_text += "Red bars: Net put premium (bearish sentiment)\n\n"
        info_text += "Features:\n"
        info_text += "- Interactive zoom and pan\n"
        info_text += "- High performance rendering\n"
        info_text += "- Real-time updates"
        
        self.info_panel.setPlainText(info_text)
        self.status_label.setText(f"Analysis complete for {ticker} ({exp_date})")
        
    def on_error(self, error_message):
        """Handle errors"""
        QMessageBox.critical(self, "Error", error_message)
        self.status_label.setText("Error occurred. Please try again.")
        
    def on_progress_update(self, message):
        """Handle progress updates"""
        self.status_label.setText(message)
        
    def on_worker_finished(self):
        """Handle worker thread completion"""
        self.progress_bar.setVisible(False)
        self.fetch_button.setEnabled(True)
        if self.current_stock:
            self.analyze_button.setEnabled(True)


def main():
    app = QApplication(sys.argv)
    window = OptionsAnalysisGUI()
    window.show()
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
