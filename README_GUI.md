# Options Analysis GUI

A PyQt6-based graphical user interface for analyzing stock options data, converted from the original command-line `options_analysis.py` script.

## 🎉 **Chart Display Fixed!**

The GUI now features large, professional-quality charts that are easy to read and analyze.

### **Chart Improvements:**
- **Large Size**: 20" wide × dynamic height (minimum 16") figures
- **High Resolution**: 200 DPI for crisp, clear display
- **Professional Fonts**: 18pt axis labels, 22pt titles, 14pt tick labels
- **Value Labels**: Premium amounts displayed directly on significant bars
- **Enhanced Visibility**: 90% bar height, improved grid lines
- **Scrollable Display**: Large charts in scrollable containers for easy navigation

## Features

- **Interactive GUI**: Easy-to-use graphical interface with input fields and buttons
- **Real-time Data**: Fetches live options data using yfinance
- **Large, High-Quality Charts**: Professional visualization with excellent readability
- **Multi-threading**: Non-blocking data fetching to keep the GUI responsive
- **Detailed Information**: Shows comprehensive analysis results in an info panel
- **Optimized Layout**: 1600x1000 window with proper space allocation

## Requirements

- Python 3.7+
- PyQt6
- yfinance
- pandas
- matplotlib

## Installation

1. Ensure you have the required packages installed:
```bash
pip install PyQt6 yfinance pandas matplotlib
```

2. Run the GUI application:
```bash
python options_analysis_gui.py
```

## How to Use

### 1. Enter Ticker Symbol
- Type a stock ticker symbol (e.g., AAPL, MSFT, TSLA) in the "Ticker" field
- Press Enter or click "Fetch Options" to load the data

### 2. Select Expiration Date
- Once data is loaded, the "Expiration" dropdown will populate with available dates
- Select your desired expiration date from the dropdown

### 3. Analyze Options
- Click the "Analyze" button to generate the analysis
- The chart will display in the main area
- Detailed information will appear in the right panel

### 4. Interpret Results

#### Chart Interpretation:
- **Green bars**: Net call premium (bullish sentiment) - more money spent on calls than puts
- **Red bars**: Net put premium (bearish sentiment) - more money spent on puts than calls
- **X-axis**: Net premium in millions of USD
- **Y-axis**: Strike prices (highest at top)

#### Information Panel:
- Total call/put volume and premium
- Number of available contracts
- Analysis summary

## GUI Components

### Main Window Layout:
1. **Input Section** (top):
   - Ticker input field
   - Fetch Options button
   - Expiration date dropdown
   - Analyze button

2. **Progress Bar**: Shows loading status during data fetching

3. **Status Label**: Displays current operation status

4. **Main Content** (split view):
   - **Left Panel**: Interactive chart display with scroll capability
   - **Right Panel**: Detailed analysis information

## Features Comparison

| Feature | Original CLI | New GUI |
|---------|-------------|---------|
| Ticker Input | Command line prompt | Text input field |
| Expiration Selection | Numbered list | Dropdown menu |
| Data Fetching | Blocking | Non-blocking (threaded) |
| Visualization | Popup window | Embedded chart |
| Multiple Analysis | Loop-based | Button-based |
| User Experience | Text-based | Visual interface |

## Error Handling

The GUI includes comprehensive error handling for:
- Invalid ticker symbols
- Network connectivity issues
- Missing options data
- Data processing errors

Error messages are displayed in popup dialogs with clear explanations.

## Technical Details

### Architecture:
- **Main Thread**: GUI operations and user interactions
- **Worker Threads**: Data fetching and processing to prevent GUI freezing
- **Signal/Slot System**: Communication between threads

### Data Processing:
- Same core logic as the original script
- Premium calculation: `volume × lastPrice × 100`
- Net premium: `call_premium - put_premium`

### Visualization:
- Uses matplotlib with Agg backend for compatibility
- Renders plots as PNG images displayed in QLabel widgets
- Supports zooming and scrolling for large charts

## Testing

Run the test suite to verify functionality:
```bash
python test_gui.py
```

This will test:
- Ticker validation
- Options data availability
- GUI component creation

## Troubleshooting

### Common Issues:

1. **Import Errors**: Ensure all required packages are installed
2. **No Options Data**: Some tickers may not have options available
3. **Network Issues**: Check internet connection for data fetching
4. **Display Issues**: Try running with different matplotlib backends if needed

### Debug Mode:
Add debug prints by modifying the worker thread progress updates.

## Future Enhancements

Potential improvements for future versions:
- Save/export chart functionality
- Historical data comparison
- Multiple ticker comparison
- Custom date range selection
- Advanced filtering options
- Portfolio analysis features

## License

This GUI application maintains the same license as the original options_analysis.py script.
