#!/usr/bin/env python3
"""
Simple launcher script for the Options Analysis GUI
"""

import sys
import subprocess
import importlib.util

def check_dependencies():
    """Check if all required dependencies are installed"""
    required_packages = {
        'PyQt6': 'PyQt6',
        'yfinance': 'yfinance', 
        'pandas': 'pandas',
        'matplotlib': 'matplotlib'
    }
    
    missing_packages = []
    
    for package_name, import_name in required_packages.items():
        spec = importlib.util.find_spec(import_name)
        if spec is None:
            missing_packages.append(package_name)
    
    if missing_packages:
        print("Missing required packages:")
        for package in missing_packages:
            print(f"  - {package}")
        print("\nTo install missing packages, run:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def main():
    """Main launcher function"""
    print("Options Analysis GUI Launcher")
    print("=" * 30)
    
    # Check dependencies
    print("Checking dependencies...")
    if not check_dependencies():
        print("\n❌ Missing dependencies. Please install them and try again.")
        return 1
    
    print("✅ All dependencies found!")
    
    # Launch the GUI
    print("\n🚀 Launching Options Analysis GUI...")
    try:
        from options_analysis_gui import main as gui_main
        gui_main()
    except KeyboardInterrupt:
        print("\n👋 GUI closed by user")
        return 0
    except Exception as e:
        print(f"\n❌ Error launching GUI: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
