import yfinance as yf
import pandas as pd
import matplotlib.pyplot as plt

# 1. Ticker selector
def get_ticker_input():
    """Get ticker symbol from user input with validation"""
    while True:
        ticker = input("Enter stock ticker symbol (e.g., AAPL, MSFT, TSLA): ").strip().upper()
        if not ticker:
            print("Please enter a valid ticker symbol.")
            continue

        # Basic validation - check if ticker exists
        try:
            stock = yf.Ticker(ticker)
            # Try to get basic info to validate ticker exists
            info = stock.info
            if 'symbol' not in info or not info.get('symbol'):
                print(f"Ticker '{ticker}' not found. Please try again.")
                continue

            # Check if options are available
            if not hasattr(stock, 'options') or not stock.options:
                print(f"No options data available for '{ticker}'. Please try a different ticker.")
                continue

            return ticker
        except Exception as e:
            print(f"Error validating ticker '{ticker}': {e}")
            print("Please try again.")

# Get ticker from user
ticker = get_ticker_input()
print(f"Analyzing options for {ticker}...")

def select_expiration_date(stock):
    """Let user select expiration date or use nearest"""
    options_dates = stock.options
    if not options_dates:
        raise ValueError("No options available for this ticker")

    print(f"\nAvailable expiration dates:")
    for i, date in enumerate(options_dates[:5]):  # Show first 5 dates
        print(f"{i+1}. {date}")

    if len(options_dates) > 5:
        print(f"... and {len(options_dates) - 5} more dates")

    while True:
        choice = input(f"\nSelect expiration date (1-{min(5, len(options_dates))}) or press Enter for nearest: ").strip()

        if not choice:  # Use nearest (first) expiration
            return options_dates[0]

        try:
            index = int(choice) - 1
            if 0 <= index < min(5, len(options_dates)):
                return options_dates[index]
            else:
                print(f"Please enter a number between 1 and {min(5, len(options_dates))}")
        except ValueError:
            print("Please enter a valid number or press Enter for nearest expiration")

# 2. Fetch option chain for the selected expiration
stock = yf.Ticker(ticker)
exp_date = select_expiration_date(stock)
print(f"Selected expiration date: {exp_date}")

try:
    chain = stock.option_chain(exp_date)
    print(f"Fetched options data for {ticker} expiring {exp_date}")
except Exception as e:
    print(f"Error fetching options data: {e}")
    exit(1)

# 2. Compute premium spent: volume * lastPrice * contract size (100)
calls = chain.calls.copy()
puts  = chain.puts.copy()
calls['premium_spent'] = calls['volume'] * calls['lastPrice'] * 100
puts['premium_spent']  = puts['volume']  * puts['lastPrice']  * 100

# 3. Merge calls & puts on strike, fill missing with zero
df = pd.merge(calls[['strike','premium_spent']],
              puts[['strike','premium_spent']],
              on='strike', how='outer',
              suffixes=('_call','_put')).fillna(0)

# 4. Compute net premium (call minus put)
df['net_premium'] = df['premium_spent_call'] - df['premium_spent_put']

# 5. Sort by strike and set up colors
df = df.sort_values('strike')
colors = df['net_premium'].apply(lambda x: 'green' if x>0 else 'red')

# 6. Plot horizontal bar chart
plt.figure(figsize=(8,6))
plt.barh(df['strike'].astype(str), df['net_premium']/1e6, color=colors)
plt.axvline(0, color='black', linewidth=0.8)

# 7. Formatting
plt.xlabel('Net Premium (Millions USD)')
plt.ylabel('Strike')
plt.title(f'{ticker} Net Premium by Strike ({exp_date})')
plt.grid(axis='x', linestyle='--', alpha=0.5)

# 8. Flip the y-axis so highest strikes on top
plt.gca().invert_yaxis()

# 9. Show plot
plt.tight_layout()
plt.show()

# 10. Ask if user wants to analyze another ticker
def main_loop():
    """Main loop to allow analyzing multiple tickers"""
    while True:
        choice = input("\nWould you like to analyze another ticker? (y/n): ").strip().lower()
        if choice in ['y', 'yes']:
            print("\n" + "="*50)
            # Get new ticker and run analysis again
            new_ticker = get_ticker_input()
            print(f"Analyzing options for {new_ticker}...")

            new_stock = yf.Ticker(new_ticker)
            new_exp_date = select_expiration_date(new_stock)
            print(f"Selected expiration date: {new_exp_date}")

            try:
                new_chain = new_stock.option_chain(new_exp_date)
                print(f"Fetched options data for {new_ticker} expiring {new_exp_date}")

                # Repeat the analysis for new ticker
                new_calls = new_chain.calls.copy()
                new_puts = new_chain.puts.copy()
                new_calls['premium_spent'] = new_calls['volume'] * new_calls['lastPrice'] * 100
                new_puts['premium_spent'] = new_puts['volume'] * new_puts['lastPrice'] * 100

                new_df = pd.merge(new_calls[['strike','premium_spent']],
                                  new_puts[['strike','premium_spent']],
                                  on='strike', how='outer',
                                  suffixes=('_call','_put')).fillna(0)

                new_df['net_premium'] = new_df['premium_spent_call'] - new_df['premium_spent_put']
                new_df = new_df.sort_values('strike')
                new_colors = new_df['net_premium'].apply(lambda x: 'green' if x>0 else 'red')

                plt.figure(figsize=(8,6))
                plt.barh(new_df['strike'].astype(str), new_df['net_premium']/1e6, color=new_colors)
                plt.axvline(0, color='black', linewidth=0.8)
                plt.xlabel('Net Premium (Millions USD)')
                plt.ylabel('Strike')
                plt.title(f'{new_ticker} Net Premium by Strike ({new_exp_date})')
                plt.grid(axis='x', linestyle='--', alpha=0.5)
                plt.gca().invert_yaxis()
                plt.tight_layout()
                plt.show()

            except Exception as e:
                print(f"Error analyzing {new_ticker}: {e}")

        elif choice in ['n', 'no']:
            print("Thank you for using the options analyzer!")
            break
        else:
            print("Please enter 'y' for yes or 'n' for no")

# Start the main loop
main_loop()
